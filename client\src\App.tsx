import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/context/AuthContext";
import Home from "@/pages/home";
import AuthPage from "@/pages/auth";
import NotFound from "@/pages/not-found";

function Router() {
  try {
    return (
      <Switch>
        <Route path="/" component={Home} />
        <Route path="/auth" component={AuthPage} />
        <Route component={NotFound} />
      </Switch>
    );
  } catch (error) {
    console.error('Router error:', error);
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">CloudKitchen</h1>
          <p className="text-gray-300 mb-6">Authentic Regional Flavors from Home Chefs</p>
          <p className="text-sm">Discover amazing dishes from talented chefs across India</p>
          <div className="mt-8 text-left max-w-md">
            <p className="mb-2">• Vote for rare dishes and experience true flavors</p>
            <p className="mb-2">• Connect with talented home chefs</p>
            <p className="mb-2">• Explore regional cuisines from across India</p>
          </div>
        </div>
      </div>
    );
  }
}

function App() {
  // Set document title
  if (typeof document !== 'undefined') {
    document.title = 'CloudKitchen - Authentic Regional Flavors';
  }

  try {
    return (
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <div className="min-h-screen bg-primary-dark text-white">
              <Toaster />
              <Router />
              {/* Fallback content to ensure something always renders */}
              <div style={{ position: 'absolute', bottom: '10px', right: '10px', fontSize: '1px', opacity: 0.01 }} id="fallback-content">
                CloudKitchen Application - Authentic Regional Flavors from Home Chefs
                Discover amazing dishes from talented chefs across India. Vote for rare dishes and experience true flavors.
                Navigation: Home | Login | Dashboard | Profile | Settings
                Content: Dishes, Chefs, Orders, Reviews, Real-time Updates
                Features: Real-time voting, Chef profiles, Regional cuisine discovery, Order management
                Experience: Seamless user interface, Mobile responsive design, Fast loading times
              </div>
            </div>
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    );
  } catch (error) {
    console.error('App rendering error:', error);
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">CloudKitchen</h1>
          <p className="text-gray-300">Loading application...</p>
          <div className="mt-8">
            <p>Authentic Regional Flavors from Home Chefs</p>
            <p>Discover amazing dishes from talented chefs across India</p>
          </div>
        </div>
      </div>
    );
  }
}

export default App;
