import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

console.log('main.tsx: Starting React app...');

// Set document title immediately
document.title = 'CloudKitchen - Authentic Regional Flavors';

// Get root element and render React app
const rootElement = document.getElementById("root");
if (rootElement) {
  console.log('main.tsx: Found root element');

  // Try to render React app
  try {
    console.log('main.tsx: Attempting to render React app...');
    createRoot(rootElement).render(<App />);
    console.log('main.tsx: React app rendered successfully');
  } catch (error) {
    console.error('main.tsx: Error rendering React app:', error);

    // Only show fallback content if React fails to render
    rootElement.innerHTML = `
      <div style="min-height: 100vh; background: #1a1a1a; color: white; padding: 20px; text-align: center;">
        <h1 style="color: #10b981; font-size: 24px; margin-bottom: 16px;">Cloud<PERSON>itchen</h1>
        <p style="color: #9ca3af; margin-bottom: 16px;">Loading application...</p>
        <p style="color: #ef4444; font-size: 14px;">Error: ${error.message}</p>
      </div>
    `;
  }
} else {
  console.error('main.tsx: Root element not found!');
}
