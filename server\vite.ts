import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer, createLogger } from "vite";
import { type Server } from "http";
import { nanoid } from "nanoid";
import { fileURLToPath } from 'url';
import { dirname } from 'path';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const viteLogger = createLogger();

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  log("Starting Vite setup...");

  const clientRoot = path.resolve(__dirname, "..", "client");
  log(`Client root directory: ${clientRoot}`);

  // Check if client directory exists
  if (!fs.existsSync(clientRoot)) {
    log(`ERROR: Client directory does not exist: ${clientRoot}`);
    throw new Error(`Client directory not found: ${clientRoot}`);
  }

  // Check if index.html exists
  const indexPath = path.resolve(clientRoot, "index.html");
  if (!fs.existsSync(indexPath)) {
    log(`ERROR: index.html not found: ${indexPath}`);
    throw new Error(`index.html not found: ${indexPath}`);
  }

  log(`Found index.html at: ${indexPath}`);

  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true as true,
  };

  log("Creating Vite server...");

  const vite = await createViteServer({
    configFile: false,
    root: clientRoot,
    plugins: [
      // Add React plugin for proper JSX handling
      (await import("@vitejs/plugin-react")).default(),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "..", "client", "src"),
        "@shared": path.resolve(__dirname, "..", "shared"),
        "@assets": path.resolve(__dirname, "..", "attached_assets"),
      },
    },
    optimizeDeps: {
      noDiscovery: false, // Enable discovery to find dependencies
      include: ["react", "react-dom", "react-dom/client"],
    },
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        log(`Vite error: ${msg}`);
        viteLogger.error(msg, options);
        // Don't exit on error, just log it
      },
    },
    server: serverOptions,
    appType: "custom",
  });

  log("Vite server created successfully");

  log("Setting up Vite middlewares...");
  app.use(vite.middlewares);

  log("Setting up catch-all route for SPA...");
  app.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    log(`Serving SPA route: ${url}`);

    try {
      const clientTemplate = path.resolve(
        __dirname,
        "..",
        "client",
        "index.html",
      );

      log(`Reading template from: ${clientTemplate}`);

      // always reload the index.html file from disk incase it changes
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      log(`Template length: ${template.length} characters`);

      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`,
      );

      log("Transforming HTML with Vite...");
      const page = await vite.transformIndexHtml(url, template);
      log(`Transformed page length: ${page.length} characters`);

      res.status(200).set({ "Content-Type": "text/html" }).end(page);
      log(`Successfully served page for ${url}`);
    } catch (e) {
      log(`Error serving page for ${url}: ${(e as Error).message}`);
      vite.ssrFixStacktrace(e as Error);
      next(e);
    }
  });

  log("Vite setup completed successfully");
}

export function serveStatic(app: Express) {
  const distPath = path.resolve(__dirname, "public");

  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`,
    );
  }

  app.use(express.static(distPath));

  // fall through to index.html if the file doesn't exist
  app.use("*", (_req, res) => {
    res.sendFile(path.resolve(distPath, "index.html"));
  });
}
