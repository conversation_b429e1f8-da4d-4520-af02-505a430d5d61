# Page snapshot

```yaml
- region "Notifications (F8)":
  - list
- navigation:
  - heading "CloudKitchen" [level=1]
  - button "🍽️Customer"
  - button:
    - img
  - button "R"
- main:
  - heading "CloudKitchen Dashboard" [level=1]
  - paragraph: Welcome to CloudKitchen - Authentic Regional Flavors from Home Chefs
  - heading "🍽️ Discover Dishes" [level=3]
  - paragraph: Explore authentic regional dishes from talented home chefs across India
  - heading "⭐ Vote & Review" [level=3]
  - paragraph: Vote for rare dishes and help chefs reach minimum orders
  - heading "👨‍🍳 Connect with Chefs" [level=3]
  - paragraph: Connect with talented home chefs in your area
  - heading "Authentic Hyderabadi Biryani" [level=4]
  - paragraph: Traditional aromatic rice dish with tender mutton and exotic spices
  - text: ₹299 45 min
  - heading "Manipuri Iromba" [level=4]
  - paragraph: Traditional fermented fish curry with fresh vegetables
  - text: ₹199 30 min
  - heading "Bihar Litti Chokha" [level=4]
  - paragraph: Rustic specialty with sattu filling and traditional accompaniments
  - text: ₹149 25 min
- text: "CloudKitchen Application - Authentic Regional Flavors from Home Chefs Discover amazing dishes from talented chefs across India. Vote for rare dishes and experience true flavors. Navigation: Home | Login | Dashboard | Profile | Settings Content: Dishes, Chefs, Orders, Reviews, Real-time Updates Features: Real-time voting, Chef profiles, Regional cuisine discovery, Order management Experience: Seamless user interface, Mobile responsive design, Fast loading times"
```