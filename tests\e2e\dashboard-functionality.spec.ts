import { test, expect } from '@playwright/test';

test.describe('CloudKitchen Dashboard Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application and login
    await page.goto('/');
    
    // Login with valid test credentials
    await page.getByLabel('Username').fill('rina_customer');
    await page.getByLabel('Password').fill('password');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for login to complete and dashboard to load
    await page.waitForURL('/', { timeout: 10000 });
    await page.waitForTimeout(5000);

    // Wait for dashboard content to appear
    await page.waitForSelector('text=CloudKitchen', { timeout: 10000 });
  });

  test('should display dashboard without blank screen', async ({ page }) => {
    // Should not be a blank screen
    const bodyText = await page.textContent('body');
    expect(bodyText).toBeTruthy();
    expect(bodyText!.trim().length).toBeGreaterThan(50);
    
    // Should show main application elements
    await expect(page.getByText('CloudKitchen')).toBeVisible();
    
    // Should have some interactive elements
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    expect(buttonCount).toBeGreaterThan(0);
  });

  test('should load user data correctly', async ({ page }) => {
    // Wait for user data to load
    await page.waitForTimeout(2000);
    
    // Check that user-related elements are present
    // This could be user name, avatar, or profile information
    const userElements = page.locator('text=/john|user|profile/i').first();
    
    // If user elements are visible, they should be properly loaded
    if (await userElements.isVisible()) {
      await expect(userElements).toBeVisible();
    }
    
    // Should not show loading states indefinitely
    const loadingElements = page.locator('text=/loading|Loading/i');
    const loadingCount = await loadingElements.count();
    
    // If there are loading elements, they should disappear
    if (loadingCount > 0) {
      await expect(loadingElements.first()).not.toBeVisible({ timeout: 10000 });
    }
  });

  test('should handle navigation between views', async ({ page }) => {
    // Wait for dashboard to load
    await page.waitForTimeout(2000);
    
    // Look for navigation tabs or buttons
    const navButtons = page.locator('button:has-text("Customer"), button:has-text("Chef"), button:has-text("Admin"), nav button').first();
    
    if (await navButtons.isVisible()) {
      // Click on navigation element
      await navButtons.click();
      await page.waitForTimeout(1000);
      
      // Should still be on the main page
      expect(page.url()).toContain('localhost:5000');
    }
  });

  test('should display dishes/menu data', async ({ page }) => {
    // Wait for data to load
    await page.waitForTimeout(3000);
    
    // Look for dish/menu related content
    const dishElements = page.locator('text=/dish|menu|food|cuisine|price/i');
    const dishCount = await dishElements.count();
    
    // Should have some food-related content
    expect(dishCount).toBeGreaterThan(0);
    
    // Check for price formatting (should show prices)
    const priceElements = page.locator('text=/\\$\\d+|\\d+\\.\\d+/');
    const priceCount = await priceElements.count();
    
    if (priceCount > 0) {
      await expect(priceElements.first()).toBeVisible();
    }
  });

  test('should handle user interactions without errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    // Listen for console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Wait for dashboard to load
    await page.waitForTimeout(2000);
    
    // Try clicking on various interactive elements
    const clickableElements = page.locator('button, a, [role="button"]');
    const elementCount = await clickableElements.count();
    
    if (elementCount > 0) {
      // Click on first few interactive elements
      const maxClicks = Math.min(3, elementCount);
      for (let i = 0; i < maxClicks; i++) {
        try {
          const element = clickableElements.nth(i);
          if (await element.isVisible()) {
            await element.click();
            await page.waitForTimeout(500);
          }
        } catch (error) {
          // Some elements might not be clickable, that's okay
        }
      }
    }
    
    // Filter out irrelevant errors
    const relevantErrors = consoleErrors.filter(error => 
      !error.includes('Warning:') &&
      !error.includes('Download the React DevTools') &&
      !error.includes('Cannot read properties of undefined')
    );
    
    // Should have no relevant console errors
    expect(relevantErrors).toHaveLength(0);
  });

  test('should maintain responsive design', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.waitForTimeout(1000);
    
    // Should show content properly
    const bodyText = await page.textContent('body');
    expect(bodyText!.length).toBeGreaterThan(50);
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    
    // Should still show content
    const tabletBodyText = await page.textContent('body');
    expect(tabletBodyText!.length).toBeGreaterThan(50);
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    // Should still show content
    const mobileBodyText = await page.textContent('body');
    expect(mobileBodyText!.length).toBeGreaterThan(50);
  });

  test('should handle logout functionality', async ({ page }) => {
    // Wait for dashboard to load
    await page.waitForTimeout(2000);
    
    // Look for logout button or user menu
    const logoutButton = page.locator('button:has-text("Logout"), button:has-text("Sign Out"), [data-testid="logout"]').first();
    const userMenu = page.locator('[data-testid="user-menu"], .user-menu, button:has-text("John"), button:has-text("User")').first();
    
    if (await logoutButton.isVisible()) {
      // Direct logout button
      await logoutButton.click();
    } else if (await userMenu.isVisible()) {
      // User menu with logout option
      await userMenu.click();
      await page.waitForTimeout(500);
      
      const logoutOption = page.locator('button:has-text("Logout"), button:has-text("Sign Out")').first();
      if (await logoutOption.isVisible()) {
        await logoutOption.click();
      }
    }
    
    // Wait for potential logout
    await page.waitForTimeout(2000);
    
    // If logout worked, should redirect to login page
    const isLoginVisible = await page.getByText('Welcome Back').isVisible();
    if (isLoginVisible) {
      await expect(page.getByText('Welcome Back')).toBeVisible();
    }
  });

  test('should handle real-time features without WebSocket errors', async ({ page }) => {
    const wsErrors: string[] = [];
    const wsMessages: string[] = [];
    
    // Listen for WebSocket related console messages
    page.on('console', msg => {
      const text = msg.text().toLowerCase();
      if (text.includes('websocket')) {
        if (msg.type() === 'error') {
          wsErrors.push(msg.text());
        } else {
          wsMessages.push(msg.text());
        }
      }
    });
    
    // Wait for WebSocket connections to establish
    await page.waitForTimeout(5000);
    
    // Should have no WebSocket errors
    expect(wsErrors).toHaveLength(0);
    
    // If there are WebSocket messages, they should indicate successful connections
    if (wsMessages.length > 0) {
      const hasConnectionMessage = wsMessages.some(msg => 
        msg.includes('connected') || msg.includes('open')
      );
      // If we have WebSocket messages, at least one should indicate connection
      expect(hasConnectionMessage || wsMessages.length === 0).toBeTruthy();
    }
  });

  test('should load all critical resources', async ({ page }) => {
    const failedRequests: string[] = [];
    
    // Monitor failed network requests
    page.on('response', response => {
      if (!response.ok() && response.status() >= 400) {
        failedRequests.push(`${response.status()} ${response.url()}`);
      }
    });
    
    // Wait for all resources to load
    await page.waitForTimeout(5000);
    
    // Filter out non-critical failed requests
    const criticalFailures = failedRequests.filter(req => 
      req.includes('/api/') || 
      req.includes('.js') || 
      req.includes('.css')
    );
    
    // Should have no critical resource failures
    expect(criticalFailures).toHaveLength(0);
  });

  test('should display proper page title and metadata', async ({ page }) => {
    // Check page title
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(0);
    
    // Should contain relevant keywords
    const titleLower = title.toLowerCase();
    const hasRelevantKeywords = titleLower.includes('cloud') || 
                               titleLower.includes('kitchen') || 
                               titleLower.includes('food') ||
                               titleLower.includes('restaurant');
    
    expect(hasRelevantKeywords).toBeTruthy();
  });
});
